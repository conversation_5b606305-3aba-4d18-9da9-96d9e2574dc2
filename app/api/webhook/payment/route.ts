import { CreditHistoryType, MembershipPeriodYear } from "@/@types/membership-type";
import { WEBNAME } from "@/lib/constants";
import { getKVKeyUser } from "@/lib/utils";
import { getMembershipProductInfo, OrderSource, getTopUpOrderProductInfo } from "@/lib/utils-membership";
import { getDB } from "@/server/db/db-client.server";
import {
	NewOrder,
	orderSchema,
	NewUserCreditsHistory,
	userCreditsHistorySchema,
	userSchema,
	NewSubscription,
	subscriptionSchema,
} from "@/server/db/schema.server";
import { notifyPurchaseEvent } from "@/server/dev-notify.server";
import { deleteValue } from "@/server/kv/redis-upstash.server";
import { revokeUserMembershipWithSubscription, resetUserSubscriptionResume } from "@/server/utils-user.server";
import { Webhooks } from "@polar-sh/nextjs";
import { Order } from "@polar-sh/sdk/models/components/order";
import { OrderSubscription } from "@polar-sh/sdk/models/components/ordersubscription";
import { Subscription } from "@polar-sh/sdk/models/components/subscription";
import { addMonths, addYears, subYears } from "date-fns";
import { eq, sql } from "drizzle-orm";

export const POST = Webhooks({
	webhookSecret: process.env.POLAR_WEBHOOK_SECRET!,
	onPayload: async (payload) => {
		// console.log("Received data", JSON.stringify(payload, null, 2));
		console.log("type:", payload.type);
		switch (payload.type) {
			case "order.created":
				break;
			case "order.paid":
				checkCustomerExternalId(payload.data.customer.externalId as string);
				if (payload.data.billingReason === "purchase") {
					// Handle a one-time order
					await purchasePaymentSuccessWithOrder(payload.data);
				}
				if (payload.data.billingReason === "subscription_create") {
					await subscriptionPaymentSuccessWithOrder(payload.data);
					// Handle a subscription started
				}
				if (payload.data.billingReason === "subscription_cycle") {
					await subscriptionPaymentSuccessWithOrder(payload.data);
					// Handle a subscription renewed
				}
				if (payload.data.billingReason === "subscription_update") {
					await subscriptionPaymentSuccessWithOrder(payload.data);
					// Handle a subscription upgraded or downgraded with an immediate proration invoice
				}
				break;
			case "order.updated":
				await updateOrder(payload.data);
				break;
			case "order.refunded":
				notifyPurchaseEvent(
					`${WEBNAME} - Polar Order Refund`,
					"eventName: order.refunded",
					`OrderId: ${payload.data.id}, userId: ${payload.data.customer.externalId as string}`,
					null,
				);
				break;
			case "subscription.created":
				break;
			case "subscription.updated":
				await updateSubscription(payload.data);
				break;
			case "subscription.active":
				break;
			case "subscription.canceled":
				await cancelSubscription(payload.data);
				break;
			case "subscription.uncanceled":
				await uncancelSubscription(payload.data);
				break;
			case "subscription.revoked":
				// Sent when a subscription is revoked, the user looses access immediately.
				// Happens when the subscription is canceled, or payment is past due.
				const revokedUserId = payload.data.customer.externalId as string;
				checkCustomerExternalId(revokedUserId);
				// 观察一段一段时间看看是否有问题 @2025-03-30
				await revokeUserMembershipWithSubscription(revokedUserId);
				break;
			default:
				// Handle unknown event
				console.log("Unknown event", payload.type);
				break;
		}
	},
});

function checkCustomerExternalId(userId: string | null | undefined) {
	if (!userId) {
		throw new Error("No user ID was provided.");
	}
}

// webhook(polar): order.paid - purchase/Top up
async function purchasePaymentSuccessWithOrder(data: Order) {
	const userId = data.customer.externalId as string;
	checkCustomerExternalId(userId);
	const orderId = data.id;
	const productId = data.productId;

	// 1. check if order already exists and user subscription is initialized(billingReason is not null)
	const db = getDB();
	const orders = await db.select().from(orderSchema).where(eq(orderSchema.orderId, orderId));
	if (orders.length !== 0 && orders[0].billingReason) {
		return;
	}

	// 2. get top-up order info
	const topUpOrderInfo = getTopUpOrderProductInfo(productId);
	const credits = topUpOrderInfo?.credits ?? 0;

	// 3. top up credits for order to user
	// 4. Insert credits change record to db: user_credits_history
	// 5. update order to db: order
	const insertUserCreditsHistoryData: NewUserCreditsHistory = {
		userId: userId,
		type: CreditHistoryType.Add,
		creditsOneTime: credits,
		remark: `onetime order: ${orderId}, productId: ${productId}. billingReason: ${data.billingReason}.`,
	};
	const updateOrderData = {
		source: OrderSource.Polar,
		status: data.status,
		billingReason: data.billingReason,

		subscriptionId: data.subscriptionId,
		checkoutId: data.checkoutId,
		productId: data.productId,

		currency: data.currency,
		subtotalAmount: data.subtotalAmount,
		discountAmount: data.discountAmount,
		taxAmount: data.taxAmount,
		netAmount: data.netAmount,
		totalAmount: data.totalAmount,
		refundedAmount: data.refundedAmount,
		refundedTaxAmount: data.refundedTaxAmount,

		createdAt: data.createdAt,
		updatedAt: data.modifiedAt ?? data.createdAt,
	};
	await db.transaction(async (tx) => {
		await tx.insert(userCreditsHistorySchema).values(insertUserCreditsHistoryData);
		await tx
			.update(userSchema)
			.set({
				creditOneTime: sql`${userSchema.creditOneTime} + ${credits}`,
				creditOneTimeEndsAt: addYears(data.createdAt, 1),
			})
			.where(eq(userSchema.id, userId));
		await tx
			.insert(orderSchema)
			.values({
				userId: userId,
				orderId: orderId,
				...updateOrderData,
			} as NewOrder)
			.onConflictDoUpdate({
				target: orderSchema.orderId,
				set: {
					...updateOrderData,
				},
			});
	});

	await deleteValue(getKVKeyUser(userId));
}

// webhook(polar): order.updated
async function updateOrder(data: Order) {
	const userId = data.customer.externalId as string;
	checkCustomerExternalId(userId);
	const orderId = data.id;

	const updateData: NewOrder = {
		userId: userId,

		orderId: orderId,
		source: OrderSource.Polar,
		status: data.status,

		subscriptionId: data.subscriptionId,
		checkoutId: data.checkoutId,
		productId: data.productId,

		currency: data.currency,
		subtotalAmount: data.subtotalAmount,
		discountAmount: data.discountAmount,
		taxAmount: data.taxAmount,
		netAmount: data.netAmount,
		totalAmount: data.totalAmount,
		refundedAmount: data.refundedAmount,
		refundedTaxAmount: data.refundedTaxAmount,

		createdAt: data.createdAt,
		updatedAt: data.modifiedAt ?? data.createdAt,
	};

	const db = getDB();
	await db
		.insert(orderSchema)
		.values(updateData)
		.onConflictDoUpdate({
			target: orderSchema.orderId,
			set: {
				...updateData,
			},
		});
}

// webhook(polar): order.paid - subscription_create/subscription_cycle/subscription_update
async function subscriptionPaymentSuccessWithOrder(data: Order) {
	const userId = data.customer.externalId as string;
	checkCustomerExternalId(userId);
	const orderId = data.id;

	// 1. check if order already exists and user subscription is initialized(billingReason is not null)
	const db = getDB();
	const orders = await db.select().from(orderSchema).where(eq(orderSchema.orderId, orderId));
	if (orders.length !== 0 && orders[0].billingReason) {
		return;
	}

	// 2. update user subscription
	const subscriptionData: OrderSubscription | null = data.subscription;
	if (!subscriptionData) {
		throw new Error(`No subscription data was provided. orderId: ${orderId}`);
	}
	// 3 Get membership, period and credits
	const membershipInfoProduct = getMembershipProductInfo(subscriptionData.productId);
	const membership = membershipInfoProduct?.membership;
	const period = membershipInfoProduct?.period;
	const credits = membership?.credits ?? 0;
	// next renew date
	let nextRenewDate: Date = subscriptionData.currentPeriodStart;
	if (period?.value === MembershipPeriodYear.value && subscriptionData.currentPeriodEnd) {
		// is year period
		// Check if the month of nextRenewDateTime minus one year is different from the current month
		const oneYearAgo = subYears(subscriptionData.currentPeriodEnd, 1);
		// console.log("oneYearAgo:", oneYearAgo);
		nextRenewDate = addMonths(oneYearAgo, 1);
	}
	// update user subscription
	await db
		.update(userSchema)
		.set({
			membershipId: membership?.id,
			membershipFormatted: membership?.name,
			creditFree: 0,
			creditSubscription: credits,
			creditSubscriptionEndsAt: nextRenewDate,
			subscriptionId: subscriptionData.id,
			subscriptionPeriod: period?.value,
			subscriptionInvoiceEndsAt: subscriptionData.currentPeriodEnd,
			subscriptionExpireAt: null,
		})
		.where(eq(userSchema.id, userId));

	// 4. Insert credits change record to db: user_credits_history
	const insertUserCreditsHistoryData: NewUserCreditsHistory = {
		userId: userId,
		type: CreditHistoryType.ResetSubscription,
		creditsSubscription: credits,
		remark: `Subscription credits reset for new subscription paid: ${data.billingReason}.`,
	};
	await db.insert(userCreditsHistorySchema).values(insertUserCreditsHistoryData);
	const updateData: NewOrder = {
		userId: userId,

		orderId: orderId,
		source: OrderSource.Polar,
		status: data.status,
		billingReason: data.billingReason,

		subscriptionId: data.subscriptionId,
		checkoutId: data.checkoutId,
		productId: data.productId,

		currency: data.currency,
		subtotalAmount: data.subtotalAmount,
		discountAmount: data.discountAmount,
		taxAmount: data.taxAmount,
		netAmount: data.netAmount,
		totalAmount: data.totalAmount,
		refundedAmount: data.refundedAmount,
		refundedTaxAmount: data.refundedTaxAmount,

		createdAt: data.createdAt,
		updatedAt: data.modifiedAt ?? data.createdAt,
	};
	await db
		.insert(orderSchema)
		.values(updateData)
		.onConflictDoUpdate({
			target: orderSchema.orderId,
			set: {
				...updateData,
			},
		});

	await deleteValue(getKVKeyUser(userId));
}

// webhook(polar): subscription.updated
async function updateSubscription(data: Subscription) {
	const userId = data.customer.externalId as string;
	checkCustomerExternalId(userId);
	const subscriptionId = data.id;

	const updateData: NewSubscription = {
		userId: userId,

		subscriptionId: subscriptionId,
		source: OrderSource.Polar,
		status: data.status,
		recurringInterval: data.recurringInterval,

		productId: data.productId,
		checkoutId: data.checkoutId,

		currentPeriodStartAt: data.currentPeriodStart,
		currentPeriodEndAt: data.currentPeriodEnd,
		cancelAtPeriodEnd: data.cancelAtPeriodEnd,
		canceledAt: data.canceledAt,
		startAt: data.startedAt,
		endAt: data.endedAt,
		endedAt: data.endedAt,

		customerCancellationReason: data.customerCancellationReason,

		createdAt: data.createdAt,
		updatedAt: data.modifiedAt ?? data.createdAt,
	};

	const db = getDB();
	await db
		.insert(subscriptionSchema)
		.values(updateData)
		.onConflictDoUpdate({
			target: subscriptionSchema.subscriptionId,
			set: {
				...updateData,
			},
		});

	// 该逻辑放在 subscription.revoked 中，观察一段一段时间看看是否有问题 @2025-03-30
	// if (data.status !== "active" && data.status !== "canceled" && data.status !== "trialing") {
	// 	await resetUserMembershipWithSubscription(userId);
	// }
}

// webhook(polar): subscription.canceled
async function cancelSubscription(data: Subscription) {
	const userId = data.customer.externalId as string;
	checkCustomerExternalId(userId);

	const db = getDB();
	await db
		.update(userSchema)
		.set({
			subscriptionExpireAt: data.endsAt ?? data.currentPeriodEnd,
		})
		.where(eq(userSchema.id, userId));

	await deleteValue(getKVKeyUser(userId));
}

// webhook(polar): subscription.uncanceled
async function uncancelSubscription(data: Subscription) {
	const userId = data.customer.externalId as string;
	checkCustomerExternalId(userId);

	await resetUserSubscriptionResume(userId);
}
