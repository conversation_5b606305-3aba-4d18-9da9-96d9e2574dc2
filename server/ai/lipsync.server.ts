import { ofetch } from "ofetch";
import { falGenLipSyncWithWebhook } from "./fal-config.server";
import { CALLBACK_URL_WAVESPEED } from "@/lib/constants";

export async function genSyncLipsync1_9FromFal(videoUrl: string, audioUrl: string): Promise<string> {
	const falAIEndPoint = "fal-ai/sync-lipsync";

	const payload: any = {
		model: "lipsync-1.9.0-beta",
		video_url: videoUrl,
		audio_url: audioUrl,
		// sync_mode: "bounce",
		sync_mode: "loop",
	};

	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai sync-lipsync 1.9 payload: ", payload);
		console.log("fal.ai sync-lipsync 1.9 falAIEndPoint:", falAIEndPoint);
	}

	const request_id = await falGenLipSyncWithWebhook(falAIEndPoint, payload);
	return request_id;
}

export async function genSyncLipsync2FromFal(videoUrl: string, audioUrl: string): Promise<string> {
	const falAIEndPoint = "fal-ai/sync-lipsync/v2";

	const payload: any = {
		model: "lipsync-2",
		video_url: videoUrl,
		audio_url: audioUrl,
		// sync_mode: "bounce",
		sync_mode: "loop",
	};

	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai sync-lipsync 2 payload: ", payload);
		console.log("fal.ai sync-lipsync 2 falAIEndPoint:", falAIEndPoint);
	}

	const request_id = await falGenLipSyncWithWebhook(falAIEndPoint, payload);
	return request_id;
}

export async function genSyncLipsync2ProFromFal(videoUrl: string, audioUrl: string): Promise<string> {
	const falAIEndPoint = "fal-ai/sync-lipsync/v2";

	const payload: any = {
		model: "lipsync-2-pro",
		video_url: videoUrl,
		audio_url: audioUrl,
		// sync_mode: "bounce",
		sync_mode: "loop",
	};

	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai sync-lipsync 2 payload: ", payload);
		console.log("fal.ai sync-lipsync 2 falAIEndPoint:", falAIEndPoint);
	}

	const request_id = await falGenLipSyncWithWebhook(falAIEndPoint, payload);
	return request_id;
}

export async function genSyncLipsyncKlingFromFal(videoUrl: string, audioUrl: string): Promise<string> {
	const falAIEndPoint = "fal-ai/kling-video/lipsync/audio-to-video";

	const payload: any = {
		video_url: videoUrl,
		audio_url: audioUrl,
	};

	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai kling lipsync payload: ", payload);
		console.log("fal.ai kling lipsync falAIEndPoint:", falAIEndPoint);
	}

	const request_id = await falGenLipSyncWithWebhook(falAIEndPoint, payload);
	return request_id;
}

export async function genVeedLipsyncFromWaveSpeed(videoUrl: string, audioUrl: string): Promise<string> {
	const waveSpeedAIEndPoint = "https://api.wavespeed.ai/api/v3/veed/lipsync";

	const payload: any = {
		video: videoUrl,
		audio: audioUrl,
	};
	console.log("fal.ai veed lipsync payload: ", payload);
	console.log("fal.ai veed lipsync waveSpeedAIEndPoint:", waveSpeedAIEndPoint);

	const wavespeedResponse = await ofetch(`${waveSpeedAIEndPoint}?webhook=${encodeURIComponent(CALLBACK_URL_WAVESPEED)}`, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Bearer ${process.env.WAVESPEED_API_KEY}`,
		},
		body: payload,
	});

	const request_id = wavespeedResponse.data.id;
	return request_id;
}
