"use client";

import { usePathname } from "next/navigation";
import { footerBadges } from "@/@types/footer-badge";

export default function FooterBadge() {
	const pathname = usePathname();

	if (footerBadges.length === 0) return null;

	if (pathname !== "/") return null;

	return (
		<div className="-mt-2">
			<div className="container flex flex-col gap-8 pb-6">
				<div className="flex flex-wrap justify-center gap-4">
					{footerBadges.map((badge, index) => (
						<a key={index} className="h-6 shrink-0" target="_blank" href={badge.href} rel="noopener noreferrer">
							{badge.src ? (
								<img className="h-full" src={badge.src} alt={badge.alt} loading="lazy" />
							) : (
								<p className="flex h-6 items-center rounded border bg-zinc-800 px-1 py-0.5 font-mono text-[10px] text-zinc-400">
									{badge.title}
								</p>
							)}
						</a>
					))}
				</div>
			</div>
		</div>
	);
}
