import { ComponentProps } from "react";
import { cn } from "@/lib/utils";

export function HowToUseImage({
	title,
	description,
	image,
	steps,
	...props
}: {
	title?: string;
	description?: string;
	image: string;
	steps: {
		title: string;
		description?: string;
		url?: string;
	}[];
} & ComponentProps<"div">) {
	return (
		<div className="container flex flex-col items-center gap-12 px-6 py-24" {...props}>
			<div className="text-center">
				<h2 className="text-3xl font-semibold text-pretty">{title}</h2>
				{description && <p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">{description}</p>}
			</div>

			<div className={cn("grid grid-cols-1 items-center gap-4 md:grid-cols-2")}>
				<div className="mx-auto flex h-full flex-row items-center justify-center gap-2 rounded-xl bg-zinc-900 p-4 text-zinc-300 transition-all duration-300 md:gap-4 md:p-6">
					<img src={image} alt={title} className="h-full w-full rounded-lg object-cover" loading="lazy" />
				</div>

				<div className={cn("flex flex-col gap-6 rounded-xl bg-zinc-900 py-6")}>
					{steps.map((step, index) => (
						<div key={index} className="flex max-w-full flex-col justify-between px-6">
							<div className="flex flex-row items-start gap-2">
								<p className="flex h-7 w-7 shrink-0 items-center justify-center rounded-full bg-zinc-700">{index + 1}</p>
								<div className="space-y-2">
									<h3 className="text-base font-medium">{step.title}</h3>
									<p className="text-muted-foreground text-sm">{step.description}</p>
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
}
