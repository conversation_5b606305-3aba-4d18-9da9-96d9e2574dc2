"use client";

import React, { ComponentProps } from "react";
import { cn } from "@/lib/utils";

export function HowToUse({
	title,
	description,
	steps,
	...props
}: {
	title?: string;
	description?: string;
	steps: {
		title: string;
		description?: string;
		url?: string;
	}[];
} & ComponentProps<"div">) {
	return (
		<div className="container flex flex-col items-center gap-12 px-6 py-20" {...props}>
			<div className="text-center">
				<h2 className="text-3xl font-semibold text-pretty">{title}</h2>
				{description && <p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">{description}</p>}
			</div>

			<div className={cn("grid grid-cols-1 gap-4 sm:grid-cols-2 md:gap-8", steps.length > 3 ? "max-w-[1360px] xl:grid-cols-4" : "lg:grid-cols-3")}>
				{steps.map((step, index) => (
					<div key={index} className="flex max-w-full flex-col items-center justify-between gap-6">
						<div className="space-y-4 text-center">
							<p className="bg-brand-success/80 mx-auto flex h-14 w-14 shrink-0 items-center justify-center rounded-full text-xl">{index + 1}</p>
							<h3 className="text-base font-medium">{step.title}</h3>
							<p className="text-muted-foreground text-sm">{step.description}</p>
						</div>
						{step.url && (
							<div className="w-full">
								<img src={step.url} alt={step.title} className="relative w-full rounded-lg border object-cover" loading="lazy" />
							</div>
						)}
						{/* <div className="w-full">
							{step.url ? (
								<img src={step.url} alt={step.title} className="relative w-full rounded-lg border object-cover" loading="lazy" />
							) : (
								<p
									className="text-right text-6xl font-extrabold text-neutral-300 after:content-(--content) md:text-7xl"
									style={{ "--content": `"${index + 1}"` } as React.CSSProperties}
								></p>
							)}
						</div> */}
					</div>
				))}
			</div>
		</div>
	);
}
