import { type LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { ComponentProps } from "react";

export function GridSections({
	title,
	description,
	features,
	className,
	...props
}: {
	title?: string;
	description?: string;
	features: {
		title: string;
		description: string;
		icon: LucideIcon;
	}[];
} & ComponentProps<"div">) {
	return (
		<div className={cn("px-4 py-24 md:px-6", className)}>
			<div className="container flex flex-col items-center gap-12 px-0" {...props}>
				{title && (
					<div className="text-center">
						<h2 className="text-[32px] font-semibold text-pretty">{title}</h2>
						{description && <p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">{description}</p>}
					</div>
				)}

				<div className="grid w-full grid-cols-1 gap-4 sm:grid-cols-2 md:gap-8 lg:grid-cols-3">
					{features.map((feature, index) => (
						<div
							key={index}
							className="group flex flex-col items-center gap-4 rounded-xl bg-zinc-900 p-6 text-zinc-300 transition-all duration-300 md:p-8"
						>
							<div className="mb-2 flex items-center justify-center rounded-lg">
								<feature.icon className="text-brand-success size-8 transition-transform duration-300" strokeWidth={1.5} />
							</div>
							<h3 className="text-center text-base font-medium md:text-lg">{feature.title}</h3>
							<p className="text-muted-foreground -mt-2 text-center text-sm">{feature.description}</p>
						</div>
					))}
				</div>
			</div>
		</div>
	);
}
