import { MembershipID, membershipMapping } from "@/@types/membership-type";
import {
	SUBSCRIPTION_PRODUCT_ID_MONTH_STARTER,
	SUBSCRIPTION_PRODUCT_ID_YEAR_STARTER,
	SUBSCRIPTION_PRODUCT_ID_MONTH_PRO,
	SUBSCRIPTION_PRODUCT_ID_YEAR_PRO,
	SUBSCRIPTION_PRODUCT_ID_MONTH_PREMIUM,
	SUBSCRIPTION_PRODUCT_ID_YEAR_PREMIUM,
	ORDER_PRODUCT_ID_1,
	getOrderProductInfo,
	ORDER_PRODUCT_ID_2,
	ORDER_PRODUCT_ID_3,
} from "@/lib/utils-membership";

export type PricingPlan = {
	id: number;
	title: string;
	titleClassName?: string;
	description?: string;
	free: boolean;
	productId?: {
		monthly: string;
		yearly: string;
	};
	price: {
		monthly: number;
		monthForYearly: number;
		yearly: number;
	};
	currency: {
		code: string;
		symbol: string;
	};
	duration: string;
	badge?: string;
	features?: {
		description: string;
		tips?: string;
	}[];
	unFeatures?: string[];
};

export const pricingPlans: PricingPlan[] = [
	{
		id: MembershipID.Free,
		title: membershipMapping[MembershipID.Free].name,
		titleClassName: "text-brand-success",
		description: "No credit card needed",
		free: true,
		price: { monthly: 0, monthForYearly: 0, yearly: 0 },
		currency: { code: "USD", symbol: "$" },
		duration: "/ month",
		features: [
			{
				description: `${membershipMapping[MembershipID.Free].credits} monthly credits recharge`,
				tips: "Fast Model: 3 credits = 1 second of video output",
			},
			{ description: "Generate videos up to 10 seconds long" },
		],
		unFeatures: ["High quality generation", "Commercial use license", "Private generation", "Priority support"],
	},
	{
		id: MembershipID.Starter,
		titleClassName: "text-blue-500",
		title: membershipMapping[MembershipID.Starter].name,
		description: "Best for individual creators",
		free: false,
		productId: { monthly: SUBSCRIPTION_PRODUCT_ID_MONTH_STARTER, yearly: SUBSCRIPTION_PRODUCT_ID_YEAR_STARTER },
		price: { monthly: 10, monthForYearly: 7, yearly: 84 },
		currency: { code: "USD", symbol: "$" },
		duration: "/ month",
		features: [
			{
				description: `${membershipMapping[MembershipID.Starter].credits} monthly credits recharge`,
				tips: `Fast Model: 2 credits = 1 second of video output\nPro Model: 5 credits = 1 second of video output\nPro 4k Model: 8 credits = 1 second of video output`,
			},
			{ description: "Generate videos up to 30 seconds long" },
			{ description: "High quality generation" },
			{ description: "Commercial use license" },
			{ description: "Private generation" },
			{ description: "Cancel anytime" },
		],
		unFeatures: ["Priority support", "Top-up credits"],
	},
	{
		id: MembershipID.Pro,
		title: membershipMapping[MembershipID.Pro].name,
		titleClassName: "text-purple-500",
		description: "Best for professional creators",
		free: false,
		productId: { monthly: SUBSCRIPTION_PRODUCT_ID_MONTH_PRO, yearly: SUBSCRIPTION_PRODUCT_ID_YEAR_PRO },
		price: { monthly: 20, monthForYearly: 14, yearly: 168 },
		currency: { code: "USD", symbol: "$" },
		duration: "/ month",
		badge: "Most popular",
		features: [
			{
				description: `${membershipMapping[MembershipID.Pro].credits} monthly credits recharge`,
				tips: `Fast Model: 2 credits = 1 second of video output\nPro Model: 5 credits = 1 second of video output\nPro 4k Model: 8 credits = 1 second of video output`,
			},
			{ description: "Generate videos up to 5 minutes long" },
			{ description: "High quality generation" },
			{ description: "Commercial use license" },
			{ description: "Private generation" },
			{ description: "Priority support" },
			{ description: "Top-up credits" },
			{ description: "Cancel anytime" },
		],
		unFeatures: [],
	},
	{
		id: MembershipID.Premium,
		title: membershipMapping[MembershipID.Premium].name,
		titleClassName: "text-yellow-500",
		description: "Best for studios and teams",
		free: false,
		productId: { monthly: SUBSCRIPTION_PRODUCT_ID_MONTH_PREMIUM, yearly: SUBSCRIPTION_PRODUCT_ID_YEAR_PREMIUM },
		price: { monthly: 60, monthForYearly: 42, yearly: 504 },
		currency: { code: "USD", symbol: "$" },
		duration: "/ month",
		features: [
			{
				description: `${membershipMapping[MembershipID.Premium].credits} monthly credits recharge`,
				tips: `Fast Model: 2 credits = 1 second of video output\nPro Model: 5 credits = 1 second of video output\nPro 4k Model: 8 credits = 1 second of video output`,
			},
			{ description: "Generate videos up to 5 minutes long" },
			{ description: "High quality generation" },
			{ description: "Commercial use license" },
			{ description: "Private generation" },
			{ description: "Priority support" },
			{ description: "Top-up credits" },
			{ description: "Cancel anytime" },
		],
		unFeatures: [],
	},
];

export type PricingOnetime = {
	free: boolean;
	tip?: string;
	credits: number;
	description?: string;
	productId: string;
	price: number;
	currency: {
		code: string;
		symbol: string;
	};
	badge?: string;
	features?: {
		description: string;
		tips?: string;
	}[];
	unFeatures?: string[];
};

export const pricingOnetime: PricingOnetime[] = [
	{
		free: false,
		credits: getOrderProductInfo(ORDER_PRODUCT_ID_1)?.credits!,
		productId: ORDER_PRODUCT_ID_1,
		price: 5,
		currency: { code: "USD", symbol: "$" },
		features: [
			{ description: "High resolution" },
			{ description: "Fastest generation" },
			{ description: "No watermark" },
			{ description: "Commercial use" },
			{ description: "valid for 1 month" },
			{ description: "Private generation" },
		],
	},
	{
		free: false,
		credits: getOrderProductInfo(ORDER_PRODUCT_ID_3)?.credits!,
		productId: ORDER_PRODUCT_ID_3,
		price: 29,
		currency: { code: "USD", symbol: "$" },
		badge: "Most Popular",
		features: [
			{ description: "High resolution" },
			{ description: "Fastest generation" },
			{ description: "No watermark" },
			{ description: "Commercial use" },
			{ description: "valid for 1 month" },
			{ description: "Private generation" },
		],
	},
	{
		free: false,
		credits: getOrderProductInfo(ORDER_PRODUCT_ID_2)?.credits!,
		productId: ORDER_PRODUCT_ID_2,
		price: 15,
		currency: { code: "USD", symbol: "$" },
		features: [
			{ description: "High resolution" },
			{ description: "Fastest generation" },
			{ description: "No watermark" },
			{ description: "Commercial use" },
			{ description: "valid for 1 month" },
			{ description: "Private generation" },
		],
	},
	// {
	// 	free: true,
	// 	tip: "Due to high demand, free credits are not available in some countries.", //由于高需求量，某些国家不提供免费额度
	// 	credits: 20,
	// 	productId: ORDER_PRODUCT_ID_1,
	// 	price: 0,
	// 	currency: { code: "USD", symbol: "$" },
	// 	features: [{ description: "With watermark" }, { description: "Personal use only" }, { description: "Public generation" }],
	// },
];

export const pricingOnetimeFeatures: {
	description: string;
	tips?: string;
}[] = [
	{ description: "High Resolution" },
	{ description: "Image Export" },
	{ description: "Priority Support" },
	{ description: "30-day Validity" },
	{ description: "More Preset Styles (Coming soon)" },
];
